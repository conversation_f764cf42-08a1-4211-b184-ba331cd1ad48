"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useReCaptcha } from "next-recaptcha-v3";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";
import { useBookingFormSchema, BookingFormData } from "../schemas/booking-form.schema";

interface BookingSectionProps {
  conversions: { [key: string]: number };
}

export default function BookingSection({ conversions }: BookingSectionProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("IDR");
  const { executeRecaptcha } = useReCaptcha();
  const { toast } = useToast();
  
  const formSchema = useBookingFormSchema();
  const form = useForm<BookingFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      whatsapp: "",
      villaAddress: "",
      preferredDate: "",
      tier: undefined
    }
  });

  const pricingTiers = {
    basic: { name: "Basic", priceIDR: 4500000 },
    smart: { name: "Smart", priceIDR: 6000000 },
    full: { name: "Full Shield", priceIDR: 8500000 }
  };

  const formatPrice = (priceIDR: number) => {
    const convertedPrice = priceIDR * (conversions[selectedCurrency] || 1);
    return formatCurrency(convertedPrice, selectedCurrency);
  };

  const selectedTier = form.watch("tier");
  const selectedTierPrice = selectedTier ? pricingTiers[selectedTier].priceIDR : 0;

  const onSubmit = async (data: BookingFormData) => {
    setIsSubmitting(true);
    
    try {
      // Execute reCAPTCHA
      const recaptchaToken = await executeRecaptcha("booking_form");
      
      // Prepare submission data
      const submissionData = {
        ...data,
        recaptchaToken,
        selectedTierPrice,
        currency: selectedCurrency,
        convertedPrice: formatPrice(selectedTierPrice)
      };

      // TODO: Replace with actual API call
      console.log("Booking submission:", submissionData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Booking Request Submitted!",
        description: `We'll contact you within 24 hours to confirm your ${pricingTiers[data.tier].name} inspection.`,
      });

      // Reset form
      form.reset();
      
    } catch (error) {
      console.error("Booking submission error:", error);
      toast({
        variant: "destructive",
        title: "Submission Failed",
        description: "Please try again or contact us directly via WhatsApp.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get minimum date (tomorrow)
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <section id="booking-section" className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50">
      <MainContentLayout>
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Book Now
            </h2>
            <p className="text-lg text-gray-600">
              Reserve your professional villa inspection. We'll confirm your booking 
              within 24 hours and schedule your on-site visit.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Name Field */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* WhatsApp Field */}
                <FormField
                  control={form.control}
                  name="whatsapp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>WhatsApp Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="+62 812 3456 7890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Villa Address Field */}
                <FormField
                  control={form.control}
                  name="villaAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Villa Address *</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter the full villa address in Bali"
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Preferred Date Field */}
                <FormField
                  control={form.control}
                  name="preferredDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Inspection Date *</FormLabel>
                      <FormControl>
                        <Input 
                          type="date" 
                          min={minDate}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Tier Selection */}
                <FormField
                  control={form.control}
                  name="tier"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Inspection Tier *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select inspection tier" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(pricingTiers).map(([key, tier]) => (
                            <SelectItem key={key} value={key}>
                              {tier.name} - {formatPrice(tier.priceIDR)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Price Display */}
                {selectedTier && (
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-900">
                        {pricingTiers[selectedTier].name} Inspection:
                      </span>
                      <span className="text-xl font-bold text-blue-600">
                        {formatPrice(selectedTierPrice)}
                      </span>
                    </div>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-red-600 hover:bg-red-700 text-white py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isSubmitting ? (
                    "Submitting..."
                  ) : selectedTier ? (
                    `Reserve Inspection – ${formatPrice(selectedTierPrice)}`
                  ) : (
                    "Reserve Inspection"
                  )}
                </Button>
              </form>
            </Form>

            {/* Contact Info */}
            <div className="mt-8 pt-6 border-t border-gray-200 text-center">
              <p className="text-sm text-gray-600">
                Need help? Contact us directly via{" "}
                <a 
                  href="https://wa.me/6281234567890" 
                  className="text-blue-600 hover:text-blue-700 font-medium"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  WhatsApp
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
