import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import HeroSection from "./components/hero-section";
import WhyItMattersSection from "./components/why-it-matters-section";
import HowItWorksSection from "./components/how-it-works-section";
import PricingSection from "./components/pricing-section";
import BookingSection from "./components/booking-section";
import VerifyFooter from "./components/verify-footer";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Verified Bali Villa Inspection 🚨 - Property Plaza",
    description: "Avoid Bali Villa Scams – We Inspect Before You Sign. Professional villa inspections with legal verification starting from IDR 4,500,000 (€270).",
    keywords: "Bali villa inspection, property verification, villa scam prevention, legal title check, Bali real estate",
    openGraph: {
      title: "Verified Bali Villa Inspection - Avoid Villa Scams",
      description: "Professional villa inspections with legal verification starting from IDR 4,500,000",
      type: "website",
      locale: "en_US",
    },
  };
}

export default async function VerifyPage() {
  const t = await getTranslations("verify");
  const currencyConversion = await getCurrencyConversion("IDR");
  const conversion = currencyConversion.data;

  return (
    <>
      <main className="min-h-screen">
        <HeroSection />
        <WhyItMattersSection />
        <HowItWorksSection />
        <PricingSection conversions={conversion} />
        <BookingSection conversions={conversion} />
      </main>
      <VerifyFooter />
    </>
  );
}
