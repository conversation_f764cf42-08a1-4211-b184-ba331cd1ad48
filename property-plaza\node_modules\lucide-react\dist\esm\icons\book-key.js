/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const BookKey = createLucideIcon("BookKey", [
  ["path", { d: "m19 3 1 1", key: "ze14oc" }],
  ["path", { d: "m20 2-4.5 4.5", key: "1sppr8" }],
  ["path", { d: "M20 8v13a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20", key: "1ocbpn" }],
  ["path", { d: "M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H14", key: "1gfsgw" }],
  ["circle", { cx: "14", cy: "8", r: "2", key: "u49eql" }]
]);

export { <PERSON><PERSON><PERSON> as default };
//# sourceMappingURL=book-key.js.map
