import { MeasurementUnit, Span } from '../types-hoist';
import { MetricSummary } from '../types-hoist';
import { Primitive } from '../types-hoist';
import { MetricType } from './types';
/**
 * Fetches the metric summary if it exists for the passed span
 */
export declare function getMetricSummaryJsonForSpan(span: Span): Record<string, Array<MetricSummary>> | undefined;
/**
 * Updates the metric summary on a span.
 */
export declare function updateMetricSummaryOnSpan(span: Span, metricType: MetricType, sanitizedName: string, value: number, unit: MeasurementUnit, tags: Record<string, Primitive>, bucketKey: string): void;
//# sourceMappingURL=metric-summary.d.ts.map
