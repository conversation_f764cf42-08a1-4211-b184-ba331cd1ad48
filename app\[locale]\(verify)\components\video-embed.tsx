"use client";

import { useState } from "react";
import { Play } from "lucide-react";
import Image from "next/image";

export default function VideoEmbed() {
  const [isPlaying, setIsPlaying] = useState(false);

  // Placeholder voor nu - later vervangen door echte video URL
  const videoUrl = "https://www.youtube.com/embed/dQw4w9WgXcQ";
  const thumbnailUrl = "https://placehold.co/600x400/ef4444/ffffff?text=Villa+Inspection+Video";

  const handlePlay = () => {
    setIsPlaying(true);
  };

  return (
    <div className="relative w-full aspect-video rounded-xl overflow-hidden shadow-2xl bg-gray-900">
      {!isPlaying ? (
        // Video Thumbnail with Play Button
        <div className="relative w-full h-full cursor-pointer" onClick={handlePlay}>
          <Image
            src={thumbnailUrl}
            alt="Villa Inspection Video Thumbnail"
            fill
            className="object-cover"
            priority
          />
          
          {/* Play But<PERSON> Overlay */}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-20 transition-all duration-200">
            <div className="bg-red-600 hover:bg-red-700 rounded-full p-6 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110">
              <Play className="w-8 h-8 text-white ml-1" fill="currentColor" />
            </div>
          </div>

          {/* Video Duration Badge */}
          <div className="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm font-medium">
            0:20
          </div>
        </div>
      ) : (
        // Embedded Video
        <iframe
          src={`${videoUrl}?autoplay=1&rel=0&modestbranding=1`}
          title="Bali Villa Inspection Video"
          className="w-full h-full"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      )}
    </div>
  );
}
