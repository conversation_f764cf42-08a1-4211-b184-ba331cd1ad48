import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Calendar, Camera, FileText } from "lucide-react";

export default function HowItWorksSection() {
  const steps = [
    {
      step: "1️⃣",
      title: "Book Online",
      description: "Select date, inspection tier, and pay upfront",
      result: "Confirmation email + prep checklist",
      icon: <Calendar className="w-8 h-8 text-blue-600" />
    },
    {
      step: "2️⃣", 
      title: "On-Site Inspection",
      description: "Video walkthrough, title + structural check",
      result: "Raw video, photos, and notes",
      icon: <Camera className="w-8 h-8 text-green-600" />
    },
    {
      step: "3️⃣",
      title: "Report in 24 hrs",
      description: "\"Scam‑Risk Score\" + legal tips",
      result: "Video + written findings + risk summary",
      icon: <FileText className="w-8 h-8 text-purple-600" />
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <MainContentLayout>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            How It Works – 3 Steps
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our streamlined process ensures you get comprehensive villa inspection 
            results within 24 hours of our on-site visit.
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full bg-white rounded-xl shadow-lg overflow-hidden min-w-[600px]">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">
                  Step
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">
                  What Happens
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">
                  What You Receive
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {steps.map((step, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-6">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{step.step}</span>
                      <div>
                        {step.icon}
                        <div className="font-semibold text-gray-900 mt-1">
                          {step.title}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-6">
                    <p className="text-gray-700 leading-relaxed">
                      {step.description}
                    </p>
                  </td>
                  <td className="px-6 py-6">
                    <p className="text-gray-700 leading-relaxed">
                      {step.result}
                    </p>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Process Timeline Visual */}
        <div className="mt-12 flex justify-center">
          <div className="flex items-center space-x-4 md:space-x-8">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                    {index + 1}
                  </div>
                  <span className="text-sm font-medium text-gray-600 mt-2 text-center">
                    {step.title}
                  </span>
                </div>
                {index < steps.length - 1 && (
                  <div className="w-8 md:w-16 h-0.5 bg-gray-300 mx-2 md:mx-4"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
