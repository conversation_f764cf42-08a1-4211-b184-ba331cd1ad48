// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FormBuilder > should render a studio form (with tree editing dialog) 1`] = `"<div data-as="div" data-ui="Stack" data-testid="field-title" data-level="0"><div data-as="div" data-ui="Flex"><div data-as="div" data-ui="fieldHeaderContentBox"><div data-as="div" data-ui="Stack"><div data-as="div" data-ui="Flex"><label data-ui="Text" for="title"><span>Title</span></label></div></div></div></div><div><div><div data-testid="change-bar-wrapper"><div data-testid="change-bar__field-wrapper"><span data-as="span" data-ui="TextInput" data-scheme="light" data-tone="default"><span><input data-as="input" data-scheme="light" data-tone="default" id="title" type="text" value=""><span data-border="" data-scheme="light" data-tone="default"></span></span></span></div></div></div></div></div>"`;

exports[`FormBuilder > should render a studio form (without tree editing dialog) 1`] = `"<div data-as="div" data-ui="Stack" data-testid="field-title" data-level="0"><div data-as="div" data-ui="Flex"><div data-as="div" data-ui="fieldHeaderContentBox"><div data-as="div" data-ui="Stack"><div data-as="div" data-ui="Flex"><label data-ui="Text" for="title"><span>Title</span></label></div></div></div></div><div><div><div data-testid="change-bar-wrapper"><div data-testid="change-bar__field-wrapper"><span data-as="span" data-ui="TextInput" data-scheme="light" data-tone="default"><span><input data-as="input" data-scheme="light" data-tone="default" id="title" type="text" value=""><span data-border="" data-scheme="light" data-tone="default"></span></span></span></div></div></div></div></div>"`;
