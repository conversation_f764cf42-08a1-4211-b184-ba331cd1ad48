import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Shield, AlertTriangle, CheckCircle } from "lucide-react";

export default function WhyItMattersSection() {
  const benefits = [
    {
      icon: <AlertTriangle className="w-8 h-8 text-red-600" />,
      text: "Professional inspections in Bali typically start from IDR 4,500,000 (~€270) for full audits",
      highlight: "🛑"
    },
    {
      icon: <CheckCircle className="w-8 h-8 text-green-600" />,
      text: "\"We uncovered a forged land certificate – client avoided €5,000 loss\"",
      highlight: "✅"
    },
    {
      icon: <Shield className="w-8 h-8 text-blue-600" />,
      text: "Local legal/title verification & BPN-certified checks included",
      highlight: "🔒"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <MainContentLayout>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why It Matters
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Don't let villa scams ruin your Bali investment. Our professional inspections 
            have saved clients thousands of euros in potential losses.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 text-2xl">
                  {benefit.highlight}
                </div>
                <div className="flex-1">
                  <div className="mb-3">
                    {benefit.icon}
                  </div>
                  <p className="text-gray-700 leading-relaxed">
                    {benefit.text}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-6 bg-blue-50 rounded-full px-8 py-4">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">BPN Certified</span>
            </div>
            <div className="w-px h-6 bg-blue-200"></div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">Local Legal Experts</span>
            </div>
            <div className="w-px h-6 bg-blue-200"></div>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">Risk Assessment</span>
            </div>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
