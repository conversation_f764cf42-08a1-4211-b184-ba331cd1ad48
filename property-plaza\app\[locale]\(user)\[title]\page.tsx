import { <PERSON>ada<PERSON> } from "next"
import { transformDetailSeekersListing } from "@/core/infrastructures/listing/transform"
import { getLocale, getTranslations } from "next-intl/server"
import { notFound } from "next/navigation"
import { supportedLocales } from "@/lib/locale/i18n"
import ImageGallery from "./ssr/image-gallery/image-gallery"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import PropertyDetail from "./ssr/detail/property-detail"
import PropertyAction from "./ssr/action/property-action"
import { ListingContractType } from "@/core/domain/listing/listing"

import ssrApiClient from "@/core/ssr-client"
import { fetchMethod } from "@/core/utils/types"
import { ListingSeekerDetailDto } from "@/core/infrastructures/listing/dto"
import { uppercaseFirstLetter } from "@/lib/utils"
import TooManyRequestError from "./too-many-request.error"
import { SeekersSettings } from "@/stores/seekers-settings.store"
import { cookies } from "next/headers"
import { cache } from 'react'
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api"
import dynamicImport from "next/dynamic"
import SeekerPropertyTypeSearch from "@/components/navbar/seeker-property-type-search"
import { listingCategory } from "@/core/domain/listing/listing-seekers"

const RecommendationProperties = dynamicImport(() => import("./recommendation-properties"))
const PropertyMap = dynamicImport(() => import("./ssr/map/property-map"))
const PopUpContent = dynamicImport(() => import("./ssr/pop-up/pop-up-content"))
// Tambahkan konfigurasi dynamic metadata
// export const dynamic = 'force-dynamic'
// export const revalidate = 0

// Tambahkan fungsi cache untuk fetch data
const getPropertyData = async (code: string) => {
  const response = await ssrApiClient<ListingSeekerDetailDto>(
    `${process.env.NEXT_PUBLIC_SERVICE_API}/properties/${code}`,
    fetchMethod.get,
    {
      next: {
        revalidate: 60
      }
    }
  )
  return response
}


export async function generateMetadata({ params, searchParams }: {
  params: { title: string },
  searchParams: Record<string, string | string[] | undefined>
}): Promise<Metadata> {
  const locale = await getLocale()

  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  const t = await getTranslations("seeker")

  if (!searchParams.code) {
    notFound()
  }

  const response = await getPropertyData(searchParams.code as string)
  if (response.error?.status == 429 || response.data == null) {
    return {}
  }
  const property = transformDetailSeekersListing(response.data)
  if (!property) {
    return {}
  }

  const languages = [...supportedLocales, "x-default"].reduce((acc, lang) => {
    if (lang == "x-default") {
      acc[lang] = `${baseUrl}${params.title}?code=${searchParams.code as string}`
      return acc
    }
    acc[lang] = `${baseUrl}${lang}/${params.title}?code=${searchParams.code as string}`
    return acc
  }, {} as Record<string, string>)
  return {
    title: property.title + " | " + t('metadata.listingDetailPage.title'),
    description: uppercaseFirstLetter(t('metadata.listingDetailPage.description', {
      propertyType: params.title,
      listingType: property.availability.type.toLowerCase(),

    })),
    keywords: t('metadata.listingDetailPage.keywords'),
    openGraph: {
      title: property.title + " | " + t('metadata.listingDetailPage.title'),
      description: uppercaseFirstLetter(t('metadata.listingDetailPage.description', {
        propertyType: params.title,
        listingType: property.availability.type.toLowerCase(),
      })),
      type: "article",
      images: {
        url: property.images[0].image,
        width: 1200,
        height: 630,
        alt: `${property.title} – Verified Bali Property`,
      },
    },
    twitter: {
      card: "summary_large_image",
      title: property.title + " | " + t('metadata.listingDetailPage.title'),
      description: uppercaseFirstLetter(t('metadata.listingDetailPage.description', {
        propertyType: params.title,
        listingType: property.availability.type.toLowerCase(),

      })),
      images: [`https://www.property-plaza.com/images/${params.title}.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}${locale}/${params.title}?code=${searchParams.code as string}`,
      languages: {
        ...languages,
      }
    }
  }
}

export default async function DetailPropertyPage({ params, searchParams }: { params: { title: string }, searchParams: Record<string, string | string[] | undefined> }) {
  const cookiesStore = cookies()
  const setting: string | undefined = cookiesStore.get("seekers-settings")?.value
  const currency: SeekersSettings | undefined = setting ? JSON.parse(setting)?.state : undefined
  const locale: string | undefined = cookiesStore.get('NEXT_LOCALE')?.value
  const userCache: string | undefined = cookiesStore.get("user")?.value
  const decodedCookie = userCache ? decodeURIComponent(userCache) : undefined;
  const userData = decodedCookie ? JSON.parse(decodedCookie) : undefined;
  const user = userData ? JSON.parse(userData).state.seekers : undefined
  const conversionRates = await getCurrencyConversion()
  if (!searchParams.code) {
    notFound()
  }
  const response = await getPropertyData(searchParams.code as string)

  if (response.error?.status == 429) return <TooManyRequestError />
  if (response.data == null) return notFound()
  const property = transformDetailSeekersListing(response.data)
  if (!property) return notFound()
  return <div className="space-y-12 relative overflow-x-hidden pb-12">

    <ImageGallery images={property.images} user={user} />
    <MainContentLayout className=" md:grid md:grid-cols-3 lg:gap-6 space-y-0">
      <div className="md:col-span-2 md:pr-8">
        <PropertyDetail
          title={property.title}
          detail={property.detail}
          features={property.features}
          sellingPoints={property.features.sellingPoints}
          description={property.description}
        />
      </div>
      <div className="max-sm:sticky max-sm:bottom-0 md:col-span-1 hidden md:block">
        <PropertyAction
          availableAt={property.availability.availableAt}
          maxDuration={{
            value: property.availability.maxDuration || 1,
            suffix: property.availability.typeMaximumDuration
          }}
          minDuration={{
            value: property.availability.minDuration || 1,
            suffix: property.availability.typeMinimumDuration
          }}
          owner={property.owner ? {
            ownerId: property.owner.code,
            ownerName: property.owner.name,
            ownerProfileUrl: property.owner.image
          } : undefined}
          middleman={property.middleman ? {
            middlemanId: property.middleman.code,
            middlemanName: property.middleman.name,
            middlemanProfileUrl: property.middleman.image
          } : undefined}
          price={property.availability.price}
          propertyId={searchParams.code as string || ""}
          type={property.availability.type as ListingContractType}
          isFavorited={property.isFavorite}
          chatCount={property.chatCount}
          isNegotiable={property.availability.isNegotiable}
          isActiveListing={property.status == "ONLINE"}
        />
      </div>
    </MainContentLayout>
    <MainContentLayout className="space-y-6 ">
      <PropertyMap
        category={property.detail.type}
        lat={property.location.latitude}
        lng={property.location.longitude} />
    </MainContentLayout>
    <MainContentLayout className="-z-10">
      <RecommendationProperties
        currentPropertyCode={searchParams.code as string}
        lat={property.location.latitude.toString()}
        lng={property.location.longitude.toString()}
        currency={currency?.currency || "EUR"}
        locale={locale || "en"}
        conversions={conversionRates.data}
      />
    </MainContentLayout>
    <div className="w-fit md:hidden">
      <PropertyAction
        availableAt={property.availability.availableAt}
        maxDuration={{
          value: property.availability.maxDuration || 1,
          suffix: property.availability.typeMaximumDuration
        }}
        minDuration={{
          value: property.availability.minDuration || 1,
          suffix: property.availability.typeMinimumDuration
        }}
        owner={property.owner ? {
          ownerId: property.owner?.code,
          ownerName: property.owner?.name,
          ownerProfileUrl: property.owner?.image
        } : undefined}
        middleman={property.middleman ? {
          middlemanId: property.middleman.code,
          middlemanName: property.middleman.name,
          middlemanProfileUrl: property.middleman.image
        } : undefined}
        price={property.availability.price}
        propertyId={searchParams.code as string || ""}
        type={property.availability.type as ListingContractType}
        isFavorited={property.isFavorite}
        chatCount={property.chatCount}
        isNegotiable={property.availability.isNegotiable}
        isActiveListing={property.status == "ONLINE"}
      />
    </div>
    <PopUpContent />
  </div>
}