import { HandlerDataError } from '../../types-hoist';
/**
 * Add an instrumentation handler for when an error is captured by the global error handler.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addGlobalErrorInstrumentationHandler(handler: (data: HandlerDataError) => void): void;
//# sourceMappingURL=globalError.d.ts.map
