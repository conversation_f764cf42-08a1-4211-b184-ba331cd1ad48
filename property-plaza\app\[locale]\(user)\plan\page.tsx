import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { getAllSubscriptionPackagesService } from "@/core/infrastructures/subscription/service";
import { getLocale, getTranslations } from "next-intl/server";
import SubscriptionContent from "../../(user-profile)/subscription/content";
import { Subscription } from "@/core/domain/subscription/subscription";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Metadata } from "next";
import { noLoginPlanUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale()
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"

  return {
    title: t("metadata.subsriptionPlan.title"),
    description: t("metadata.subsriptionPlan.description"),
    alternates: {
      canonical: baseUrl + locale + noLoginPlanUrl,
      languages: {
        en: baseUrl + "en" + noLoginPlanUrl,
        id: baseUrl + "id" + noLoginPlanUrl,
        "x-default": baseUrl + noLoginPlanUrl.replace("/", ""),

      }
    },
    openGraph: {
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      url: baseUrl + locale + "/",
      siteName: "Property Plaza",
      type: "website",
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
    },
    robots: {
      index: true,
      follow: true
    }
  }
}

export default async function SubscriptionPlan() {
  const conversionRates = await getCurrencyConversion("EUR")
  const subscriptionPackages = await getAllSubscriptionPackagesService()
  const data = subscriptionPackages.data
  const t = await getTranslations("seeker")
  return <MainContentLayout>

    <div className="space-y-1 my-12">
      <h1 className="capitalize text-seekers-text text-2xl font-bold tracking-[0.5%]">{t('plan.title')}</h1>
      <p className=" text-seekers-text-light text-base font-semibold tracking-[0.5%]">{t('plan.description')}</p>
    </div>
    <SubscriptionContent SubscriptionPackages={data as Subscription[] || []} conversionRate={conversionRates.data} />
  </MainContentLayout>
}