"use client";

import { useState } from "react";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { Check, Star } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface PricingSectionProps {
  conversions: { [key: string]: number };
}

export default function PricingSection({ conversions }: PricingSectionProps) {
  const [selectedCurrency, setSelectedCurrency] = useState("IDR");

  const pricingTiers = [
    {
      id: "basic",
      name: "Basic",
      priceIDR: 4500000,
      features: [
        "Video walkthrough",
        "Title deed validation", 
        "Photo checklist",
        "Short voice summary"
      ],
      popular: false
    },
    {
      id: "smart",
      name: "Smart",
      priceIDR: 6000000,
      features: [
        "Everything in Basic",
        "Detailed \"Risk‑Score Report\"",
        "Highlight video",
        "Call with legal advisor"
      ],
      popular: true,
      badge: "Most Popular"
    },
    {
      id: "full",
      name: "Full Shield", 
      priceIDR: 8500000,
      features: [
        "Everything in Smart",
        "Contract review",
        "BPN‑certified endorsement",
        "Urgent booking priority"
      ],
      popular: false
    }
  ];

  const currencies = ["IDR", "EUR", "USD", "GBP"];

  const formatPrice = (priceIDR: number) => {
    const convertedPrice = priceIDR * (conversions[selectedCurrency] || 1);
    return formatCurrency(convertedPrice, selectedCurrency);
  };

  const scrollToBooking = () => {
    const bookingSection = document.getElementById('booking-section');
    bookingSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="py-16 bg-white">
      <MainContentLayout>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Pricing Tiers (Local Market Pricing)
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Choose the inspection level that matches your needs. All prices include 
            on-site visit, professional assessment, and detailed reporting.
          </p>

          {/* Currency Selector */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100 rounded-lg p-1 inline-flex">
              {currencies.map((currency) => (
                <button
                  key={currency}
                  onClick={() => setSelectedCurrency(currency)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    selectedCurrency === currency
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {currency}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
          {pricingTiers.map((tier) => (
            <div
              key={tier.id}
              className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 ${
                tier.popular ? "ring-2 ring-blue-600 lg:scale-105" : ""
              } ${tier.popular && "sm:col-span-2 lg:col-span-1"}`}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star className="w-4 h-4" fill="currentColor" />
                    <span>{tier.badge}</span>
                  </div>
                </div>
              )}

              <div className="p-8">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {tier.name}
                  </h3>
                  <div className="text-4xl font-bold text-gray-900 mb-1">
                    {formatPrice(tier.priceIDR)}
                  </div>
                  {selectedCurrency !== "IDR" && (
                    <div className="text-sm text-gray-500">
                      ≈ {formatCurrency(tier.priceIDR, "IDR")}
                    </div>
                  )}
                </div>

                <ul className="space-y-4 mb-8">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <Check className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={scrollToBooking}
                  className={`w-full py-3 font-semibold rounded-lg transition-all duration-200 ${
                    tier.popular
                      ? "bg-blue-600 hover:bg-blue-700 text-white"
                      : "bg-gray-100 hover:bg-gray-200 text-gray-900"
                  }`}
                >
                  Select {tier.name}
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Price Display Note */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            * Prices shown in {selectedCurrency}. Exchange rates updated daily.
            {selectedCurrency !== "IDR" && " Base pricing in Indonesian Rupiah (IDR)."}
          </p>
        </div>
      </MainContentLayout>
    </section>
  );
}
