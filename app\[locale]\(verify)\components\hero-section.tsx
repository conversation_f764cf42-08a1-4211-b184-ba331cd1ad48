import { Button } from "@/components/ui/button";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import VideoEmbed from "./video-embed";

export default function HeroSection() {
  const scrollToBooking = () => {
    const bookingSection = document.getElementById('booking-section');
    bookingSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="bg-gradient-to-br from-red-50 to-orange-50 py-16 md:py-24">
      <MainContentLayout>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Verified Bali Villa Inspection{" "}
                <span className="text-red-600">🚨</span>
              </h1>
              
              <h2 className="text-xl md:text-2xl text-gray-700 font-medium">
                <em>Avoid Bali Villa Scams – We Inspect Before You Sign</em>
              </h2>
              
              <p className="text-lg text-gray-600 leading-relaxed">
                "We visit the villa, inspect legal/title red flags, structural issues & hidden costs, 
                then deliver full video + risk report"
              </p>
            </div>

            <Button 
              onClick={scrollToBooking}
              size="lg"
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Book Your Bali Inspection – from IDR 4,500,000 (€270)
            </Button>
          </div>

          {/* Right Column - Video */}
          <div className="relative">
            <VideoEmbed />
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
