import { z } from "zod";

export function useBookingFormSchema() {
  const formSchema = z.object({
    name: z.string()
      .min(1, { message: "Name is required" })
      .max(50, { message: "Name is too long" }),
    
    email: z.string()
      .min(1, { message: "Email is required" })
      .email({ message: "Please enter a valid email address" }),
    
    whatsapp: z.string()
      .min(1, { message: "WhatsApp number is required" })
      .min(8, { message: "WhatsApp number is too short" }),
    
    villaAddress: z.string()
      .min(3, { message: "Villa address is too short" })
      .max(200, { message: "Villa address is too long" }),
    
    preferredDate: z.date({
      required_error: "Please select a preferred date",
      invalid_type_error: "Please select a valid date"
    }).refine((date) => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return date >= tomorrow;
    }, {
      message: "Please select a date from tomorrow onwards"
    }),
    
    tier: z.enum(["basic", "smart", "full"], {
      required_error: "Please select an inspection tier"
    })
  });

  return formSchema;
}

export type BookingFormData = z.infer<ReturnType<typeof useBookingFormSchema>>;
