import { Options, SamplingContext } from '../types-hoist';
/**
 * Makes a sampling decision for the given options.
 *
 * Called every time a root span is created. Only root spans which emerge with a `sampled` value of `true` will be
 * sent to Sentry.
 */
export declare function sampleSpan(options: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'>, samplingContext: SamplingContext): [
    /*sampled*/ boolean,
    /*sampleRate*/ number
];
//# sourceMappingURL=sampling.d.ts.map
