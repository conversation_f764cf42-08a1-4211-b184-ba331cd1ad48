export declare const ipHeaderNames: string[];
/**
 * Get the IP address of the client sending a request.
 *
 * It receives a Request headers object and use it to get the
 * IP address from one of the following headers in order.
 *
 * If the IP address is valid, it will be returned. Otherwise, null will be
 * returned.
 *
 * If the header values contains more than one IP address, the first valid one
 * will be returned.
 */
export declare function getClientIPAddress(headers: {
    [key: string]: string | string[] | undefined;
}): string | null;
//# sourceMappingURL=getIpAddress.d.ts.map