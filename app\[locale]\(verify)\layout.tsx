import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from "next-intl/server";
import { Toaster } from "@/components/ui/toaster";
import NextTopLoader from "nextjs-toploader";
import TanstackQueryProvider from "@/components/providers/tanstack-query-provider";
import RecaptchaProvider from "@/components/providers/recaptcha-provider";
import NotificationProvider from "@/components/providers/notification-provider";
import { Suspense } from "react";
import FacebookPixel from "../facebook-pixel";

const inter = Inter({ subsets: ["latin"] });

export default async function VerifyLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const messages = await getMessages();
  const { locale } = await params;

  return (
    <html lang={locale}>
      <head>
        <meta name="google-site-verification" content="4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw" />
        <title>Verified Bali Villa Inspection - Property Plaza</title>
        <meta name="description" content="Avoid Bali Villa Scams – We Inspect Before You Sign. Professional villa inspections starting from IDR 4,500,000." />
      </head>
      <NextIntlClientProvider messages={messages}>
        <RecaptchaProvider>
          <body className={`${inter.className} relative`}>
            <NotificationProvider />
            <Suspense fallback="null">
              <FacebookPixel />
            </Suspense>
            <NextTopLoader showSpinner={false} />
            <TanstackQueryProvider>
              <div className="min-h-screen bg-white">
                {children}
              </div>
              <Toaster />
            </TanstackQueryProvider>
          </body>
        </RecaptchaProvider>
      </NextIntlClientProvider>
    </html>
  );
}
