/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Logs = createLucideIcon("Logs", [
  ["path", { d: "M13 12h8", key: "h98zly" }],
  ["path", { d: "M13 18h8", key: "oe0vm4" }],
  ["path", { d: "M13 6h8", key: "15sg57" }],
  ["path", { d: "M3 12h1", key: "lp3yf2" }],
  ["path", { d: "M3 18h1", key: "1eiwyy" }],
  ["path", { d: "M3 6h1", key: "rgxa97" }],
  ["path", { d: "M8 12h1", key: "1con00" }],
  ["path", { d: "M8 18h1", key: "13wk12" }],
  ["path", { d: "M8 6h1", key: "tn6mkg" }]
]);

export { Logs as default };
//# sourceMappingURL=logs.js.map
