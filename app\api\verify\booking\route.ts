import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Validation schema for the booking request
const bookingSchema = z.object({
  name: z.string().min(1).max(50),
  email: z.string().email(),
  whatsapp: z.string().min(8),
  villaAddress: z.string().min(3).max(200),
  preferredDate: z.string().datetime(),
  tier: z.enum(["basic", "smart", "full"]),
  selectedTierPrice: z.number(),
  currency: z.string(),
  convertedPrice: z.string(),
  recaptchaToken: z.string()
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = bookingSchema.parse(body);
    
    // Verify reCAPTCHA token
    const recaptchaResponse = await fetch(
      `https://www.google.com/recaptcha/api/siteverify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${validatedData.recaptchaToken}`,
      }
    );
    
    const recaptchaResult = await recaptchaResponse.json();
    
    if (!recaptchaResult.success) {
      return NextResponse.json(
        { error: "reCAPTCHA verification failed" },
        { status: 400 }
      );
    }

    // TODO: Here you would typically:
    // 1. Save to database
    // 2. Send confirmation email to customer
    // 3. Send notification to admin/team
    // 4. Integrate with booking/calendar system
    
    console.log("Booking request received:", {
      ...validatedData,
      recaptchaToken: "[REDACTED]"
    });

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // For now, just return success
    return NextResponse.json({
      success: true,
      message: "Booking request submitted successfully",
      bookingId: `VBI-${Date.now()}`, // Generate a temporary booking ID
      data: {
        name: validatedData.name,
        email: validatedData.email,
        tier: validatedData.tier,
        preferredDate: validatedData.preferredDate,
        price: validatedData.convertedPrice
      }
    });

  } catch (error) {
    console.error("Booking submission error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Invalid form data", 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
