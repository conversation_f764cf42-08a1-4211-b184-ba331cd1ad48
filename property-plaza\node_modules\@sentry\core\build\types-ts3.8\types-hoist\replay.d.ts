import { Event } from './event';
/**
 * NOTE: These types are still considered Beta and subject to change.
 * @hidden
 */
export interface ReplayEvent extends Event {
    urls: string[];
    replay_start_timestamp?: number;
    error_ids: string[];
    trace_ids: string[];
    replay_id: string;
    segment_id: number;
    replay_type: ReplayRecordingMode;
}
/**
 * NOTE: These types are still considered Beta and subject to change.
 * @hidden
 */
export type ReplayRecordingData = string | Uint8Array;
/**
 * NOTE: These types are still considered Beta and subject to change.
 * @hidden
 */
export type ReplayRecordingMode = 'session' | 'buffer';
//# sourceMappingURL=replay.d.ts.map
