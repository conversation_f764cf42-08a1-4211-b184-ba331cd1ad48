import type { BaseTransportOptions, Envelope, EnvelopeItemType, Event, Transport } from '../types-hoist';
interface MatchParam {
    /** The envelope to be sent */
    envelope: Envelope;
    /**
     * A function that returns an event from the envelope if one exists. You can optionally pass an array of envelope item
     * types to filter by - only envelopes matching the given types will be multiplexed.
     * Allowed values are: 'event', 'transaction', 'profile', 'replay_event'
     *
     * @param types Defaults to ['event']
     */
    getEvent(types?: EnvelopeItemType[]): Event | undefined;
}
type RouteTo = {
    dsn: string;
    release: string;
};
type Matcher = (param: MatchParam) => (string | RouteTo)[];
/**
 * Gets an event from an envelope.
 *
 * This is only exported for use in the tests
 */
export declare function eventFromEnvelope(env: Envelope, types: EnvelopeItemType[]): Event | undefined;
/**
 * Creates a transport that can send events to different DSNs depending on the envelope contents.
 */
export declare function makeMultiplexedTransport<TO extends BaseTransportOptions>(createTransport: (options: TO) => Transport, matcher: Matcher): (options: TO) => Transport;
export {};
//# sourceMappingURL=multiplexed.d.ts.map